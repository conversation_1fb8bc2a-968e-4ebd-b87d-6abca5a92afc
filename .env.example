# Application Configuration
NODE_ENV=development
PORT=3000
APP_NAME=WhatsApp Bot Flow
APP_VERSION=1.0.0

# Database Configuration
DB_TYPE=sqlite
DB_PATH=./data/bot.db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=whatsapp_bot
DB_USER=your_username
DB_PASSWORD=your_password

# WhatsApp Configuration
WHATSAPP_SESSION_PATH=./sessions
WHATSAPP_PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox
WHATSAPP_TIMEOUT=60000

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
BCRYPT_ROUNDS=12
API_RATE_LIMIT_WINDOW=15
API_RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# External APIs
WEBHOOK_URL=https://your-webhook-url.com/webhook
WEBHOOK_SECRET=your-webhook-secret
EXTERNAL_API_KEY=your-external-api-key

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password
ADMIN_EMAIL=<EMAIL>

# Features
ENABLE_ANALYTICS=true
ENABLE_WEBHOOKS=true
ENABLE_FILE_UPLOADS=true
MAX_FILE_SIZE=10MB

# Development
DEBUG=whatsapp-bot:*
ENABLE_SWAGGER=true
CORS_ORIGIN=http://localhost:3000
