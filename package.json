{"name": "whatsapp-bot-flow", "version": "1.0.0", "description": "A comprehensive WhatsApp bot with conversation flow management", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js", "build": "node build.js", "docker:build": "docker build -t whatsapp-bot-flow .", "docker:run": "docker run -p 3000:3000 whatsapp-bot-flow"}, "keywords": ["whatsapp", "bot", "chatbot", "conversation-flow", "automation", "messaging"], "author": "Your Name", "license": "MIT", "dependencies": {"whatsapp-web.js": "^1.23.0", "qrcode-terminal": "^0.12.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "sequelize": "^6.35.2", "joi": "^17.11.0", "winston": "^3.11.0", "node-cron": "^3.0.3", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "rate-limiter-flexible": "^4.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "prettier": "^3.1.1", "@types/jest": "^29.5.8", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/whatsapp-bot-flow.git"}, "bugs": {"url": "https://github.com/yourusername/whatsapp-bot-flow/issues"}, "homepage": "https://github.com/yourusername/whatsapp-bot-flow#readme"}