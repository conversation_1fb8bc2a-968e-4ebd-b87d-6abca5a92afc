# WhatsApp Bot Flow

A comprehensive WhatsApp bot with advanced conversation flow management, built with Node.js and whatsapp-web.js.

## Features

- 🤖 **Advanced Bot Engine**: Powered by whatsapp-web.js with session management
- 🔄 **Conversation Flows**: Dynamic conversation flows with state management
- 📊 **Analytics**: Track user interactions and bot performance
- 🔗 **Webhooks**: Integration with external services
- 🛡️ **Security**: JWT authentication, rate limiting, and input validation
- 📱 **Multi-session**: Support for multiple WhatsApp sessions
- 🗄️ **Database**: SQLite/PostgreSQL support with Sequelize ORM
- 📝 **Logging**: Comprehensive logging with Winston
- 🧪 **Testing**: Full test suite with Jest
- 🐳 **Docker**: Ready for containerized deployment

## Quick Start

### Prerequisites

- Node.js 18+ and npm 8+
- Chrome/Chromium browser (for WhatsApp Web)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/whatsapp-bot-flow.git
cd whatsapp-bot-flow
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Initialize the database:
```bash
npm run db:migrate
npm run db:seed
```

5. Start the bot:
```bash
npm run dev
```

6. Scan the QR code with your WhatsApp mobile app when prompted.

## Project Structure

```
whatsapp-bot-flow/
├── src/
│   ├── bot/                 # WhatsApp bot core
│   ├── flows/               # Conversation flow definitions
│   ├── database/            # Database models and migrations
│   ├── api/                 # REST API endpoints
│   ├── services/            # Business logic services
│   ├── middleware/          # Express middleware
│   ├── utils/               # Utility functions
│   └── config/              # Configuration files
├── tests/                   # Test files
├── docs/                    # Documentation
├── data/                    # Database files
├── logs/                    # Log files
├── sessions/                # WhatsApp session data
└── uploads/                 # File uploads
```

## Configuration

See `.env.example` for all available configuration options.

## API Documentation

Once running, visit `http://localhost:3000/api-docs` for interactive API documentation.

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## Docker Deployment

```bash
# Build image
npm run docker:build

# Run container
npm run docker:run
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
