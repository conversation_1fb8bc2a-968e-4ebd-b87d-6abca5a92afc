import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export const config = {
  app: {
    name: process.env.APP_NAME || 'WhatsApp Bot Flow',
    version: process.env.APP_VERSION || '1.0.0',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 3000,
    debug: process.env.DEBUG === 'true' || process.env.NODE_ENV === 'development'
  },

  database: {
    type: process.env.DB_TYPE || 'sqlite',
    path: process.env.DB_PATH || join(__dirname, '../../data/bot.db'),
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    name: process.env.DB_NAME || 'whatsapp_bot',
    username: process.env.DB_USER || '',
    password: process.env.DB_PASSWORD || '',
    logging: process.env.NODE_ENV === 'development'
  },

  whatsapp: {
    sessionPath: process.env.WHATSAPP_SESSION_PATH || join(__dirname, '../../sessions'),
    puppeteerArgs: process.env.WHATSAPP_PUPPETEER_ARGS?.split(',') || [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ],
    timeout: parseInt(process.env.WHATSAPP_TIMEOUT) || 60000,
    retryDelay: 5000,
    maxRetries: 3
  },

  security: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    rateLimitWindow: parseInt(process.env.API_RATE_LIMIT_WINDOW) || 15,
    rateLimitMax: parseInt(process.env.API_RATE_LIMIT_MAX) || 100
  },

  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || join(__dirname, '../../logs/app.log'),
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
  },

  webhooks: {
    url: process.env.WEBHOOK_URL || '',
    secret: process.env.WEBHOOK_SECRET || '',
    enabled: process.env.ENABLE_WEBHOOKS === 'true'
  },

  admin: {
    username: process.env.ADMIN_USERNAME || 'admin',
    password: process.env.ADMIN_PASSWORD || 'admin123',
    email: process.env.ADMIN_EMAIL || '<EMAIL>'
  },

  features: {
    analytics: process.env.ENABLE_ANALYTICS === 'true',
    fileUploads: process.env.ENABLE_FILE_UPLOADS === 'true',
    maxFileSize: process.env.MAX_FILE_SIZE || '10MB',
    swagger: process.env.ENABLE_SWAGGER === 'true'
  },

  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  },

  paths: {
    root: join(__dirname, '../..'),
    src: join(__dirname, '..'),
    data: join(__dirname, '../../data'),
    logs: join(__dirname, '../../logs'),
    uploads: join(__dirname, '../../uploads'),
    sessions: join(__dirname, '../../sessions')
  }
};
