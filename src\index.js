import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config();

// Import modules
import { logger } from './utils/logger.js';
import { config } from './config/index.js';
import { WhatsAppBot } from './bot/WhatsAppBot.js';
import { FlowManager } from './flows/FlowManager.js';
import { DatabaseManager } from './database/DatabaseManager.js';
import { apiRoutes } from './api/index.js';
import { errorHandler } from './middleware/errorHandler.js';
import { rateLimiter } from './middleware/rateLimiter.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class Application {
  constructor() {
    this.app = express();
    this.bot = null;
    this.flowManager = null;
    this.dbManager = null;
  }

  async initialize() {
    try {
      logger.info('🚀 Starting WhatsApp Bot Flow Application...');

      // Initialize database
      this.dbManager = new DatabaseManager();
      await this.dbManager.initialize();
      logger.info('✅ Database initialized');

      // Initialize flow manager
      this.flowManager = new FlowManager(this.dbManager);
      await this.flowManager.initialize();
      logger.info('✅ Flow manager initialized');

      // Initialize WhatsApp bot
      this.bot = new WhatsAppBot(this.flowManager, this.dbManager);
      await this.bot.initialize();
      logger.info('✅ WhatsApp bot initialized');

      // Setup Express app
      this.setupExpress();
      logger.info('✅ Express server configured');

      // Start server
      this.startServer();

    } catch (error) {
      logger.error('❌ Failed to initialize application:', error);
      process.exit(1);
    }
  }

  setupExpress() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true
    }));

    // Rate limiting
    this.app.use(rateLimiter);

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: message => logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use('/uploads', express.static(join(__dirname, '../uploads')));

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: config.app.version,
        uptime: process.uptime()
      });
    });

    // API routes
    this.app.use('/api', apiRoutes(this.bot, this.flowManager, this.dbManager));

    // Error handling
    this.app.use(errorHandler);

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: 'The requested resource was not found'
      });
    });
  }

  startServer() {
    const port = config.app.port;
    this.app.listen(port, () => {
      logger.info(`🌐 Server running on port ${port}`);
      logger.info(`📱 Bot status: ${this.bot.isReady ? 'Ready' : 'Initializing'}`);
      logger.info(`🔗 API: http://localhost:${port}/api`);
      logger.info(`❤️  Health: http://localhost:${port}/health`);
    });
  }

  async shutdown() {
    logger.info('🛑 Shutting down application...');
    
    if (this.bot) {
      await this.bot.destroy();
      logger.info('✅ WhatsApp bot destroyed');
    }

    if (this.dbManager) {
      await this.dbManager.close();
      logger.info('✅ Database connection closed');
    }

    logger.info('👋 Application shutdown complete');
    process.exit(0);
  }
}

// Create and start application
const app = new Application();

// Graceful shutdown
process.on('SIGINT', () => app.shutdown());
process.on('SIGTERM', () => app.shutdown());
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  app.shutdown();
});
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  app.shutdown();
});

// Start the application
app.initialize();
